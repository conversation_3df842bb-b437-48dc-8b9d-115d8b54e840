// Mobile Navigation Toggle
document.addEventListener('DOMContentLoaded', function() {
    const hamburger = document.getElementById('hamburger');
    const navMenu = document.getElementById('nav-menu');

    if (hamburger && navMenu) {
        hamburger.addEventListener('click', function() {
            navMenu.classList.toggle('active');
            hamburger.classList.toggle('active');
        });

        // Close menu when clicking on a link
        const navLinks = document.querySelectorAll('.nav-link');
        navLinks.forEach(link => {
            link.addEventListener('click', () => {
                navMenu.classList.remove('active');
                hamburger.classList.remove('active');
            });
        });
    }
});

// Smooth Scrolling for Internal Links
document.querySelectorAll('a[href^="#"]').forEach(anchor => {
    anchor.addEventListener('click', function (e) {
        e.preventDefault();
        const target = document.querySelector(this.getAttribute('href'));
        if (target) {
            target.scrollIntoView({
                behavior: 'smooth',
                block: 'start'
            });
        }
    });
});

// Scroll Animation Observer
const observerOptions = {
    threshold: 0.1,
    rootMargin: '0px 0px -50px 0px'
};

const observer = new IntersectionObserver(function(entries) {
    entries.forEach(entry => {
        if (entry.isIntersecting) {
            entry.target.classList.add('aos-animate');
        }
    });
}, observerOptions);

// Observe all elements with data-aos attribute
document.querySelectorAll('[data-aos]').forEach(el => {
    observer.observe(el);
});

// Header Background Change on Scroll
window.addEventListener('scroll', function() {
    const header = document.querySelector('.header');
    if (window.scrollY > 100) {
        header.style.background = 'rgba(30, 58, 138, 0.95)';
        header.style.backdropFilter = 'blur(10px)';
    } else {
        header.style.background = 'linear-gradient(135deg, var(--deep-blue), var(--dark-blue))';
        header.style.backdropFilter = 'none';
    }
});

// Card Hover Effects
document.querySelectorAll('.section-card').forEach(card => {
    card.addEventListener('mouseenter', function() {
        this.style.transform = 'translateY(-10px) scale(1.02)';
    });

    card.addEventListener('mouseleave', function() {
        this.style.transform = 'translateY(0) scale(1)';
    });
});

// Islamic Pattern Animation
function createFloatingPatterns() {
    const hero = document.querySelector('.hero');
    if (!hero) return;

    for (let i = 0; i < 5; i++) {
        const pattern = document.createElement('div');
        pattern.className = 'floating-pattern';
        pattern.style.cssText = `
            position: absolute;
            width: 20px;
            height: 20px;
            background: rgba(217, 119, 6, 0.3);
            border-radius: 50%;
            animation: float ${5 + Math.random() * 5}s ease-in-out infinite;
            left: ${Math.random() * 100}%;
            top: ${Math.random() * 100}%;
            animation-delay: ${Math.random() * 2}s;
        `;
        hero.appendChild(pattern);
    }
}

// CSS for floating animation
const style = document.createElement('style');
style.textContent = `
    @keyframes float {
        0%, 100% { transform: translateY(0px) rotate(0deg); opacity: 0.3; }
        50% { transform: translateY(-20px) rotate(180deg); opacity: 0.7; }
    }

    .hamburger.active span:nth-child(1) {
        transform: rotate(-45deg) translate(-5px, 6px);
    }

    .hamburger.active span:nth-child(2) {
        opacity: 0;
    }

    .hamburger.active span:nth-child(3) {
        transform: rotate(45deg) translate(-5px, -6px);
    }
`;
document.head.appendChild(style);

// Initialize floating patterns
createFloatingPatterns();

// Page Load Animation
window.addEventListener('load', function() {
    document.body.classList.add('loaded');

    // Animate hero content
    const heroText = document.querySelector('.hero-text');
    const heroImage = document.querySelector('.hero-image');

    if (heroText) {
        heroText.style.animation = 'fadeInUp 1s ease-out';
    }

    if (heroImage) {
        heroImage.style.animation = 'fadeInUp 1s ease-out 0.3s both';
    }
});

// Quote Section Animation
const quoteSection = document.querySelector('.quote-section');
if (quoteSection) {
    const quoteObserver = new IntersectionObserver(function(entries) {
        entries.forEach(entry => {
            if (entry.isIntersecting) {
                const quote = entry.target.querySelector('.islamic-quote');
                if (quote) {
                    quote.style.animation = 'fadeInUp 1s ease-out';
                }
            }
        });
    }, { threshold: 0.3 });

    quoteObserver.observe(quoteSection);
}

// Shrine Silhouette Animation
const shrine = document.querySelector('.shrine-silhouette');
if (shrine) {
    shrine.addEventListener('mouseenter', function() {
        this.style.transform = 'scale(1.05) rotateY(5deg)';
        this.style.transition = 'all 0.3s ease';
    });

    shrine.addEventListener('mouseleave', function() {
        this.style.transform = 'scale(1) rotateY(0deg)';
    });
}

// Active Navigation Link Update
function updateActiveNavLink() {
    const currentPage = window.location.pathname.split('/').pop() || 'index.html';
    const navLinks = document.querySelectorAll('.nav-link');

    navLinks.forEach(link => {
        link.classList.remove('active');
        if (link.getAttribute('href') === currentPage) {
            link.classList.add('active');
        }
    });
}

updateActiveNavLink();

// Keyboard Navigation Support
document.addEventListener('keydown', function(e) {
    if (e.key === 'Escape') {
        const navMenu = document.getElementById('nav-menu');
        const hamburger = document.getElementById('hamburger');
        if (navMenu && navMenu.classList.contains('active')) {
            navMenu.classList.remove('active');
            hamburger.classList.remove('active');
        }
    }
});

// Performance Optimization: Throttle Scroll Events
function throttle(func, wait) {
    let timeout;
    return function executedFunction(...args) {
        const later = () => {
            clearTimeout(timeout);
            func(...args);
        };
        clearTimeout(timeout);
        timeout = setTimeout(later, wait);
    };
}

// Apply throttling to scroll events
const throttledScrollHandler = throttle(function() {
    const header = document.querySelector('.header');
    if (window.scrollY > 100) {
        header.style.background = 'rgba(30, 58, 138, 0.95)';
        header.style.backdropFilter = 'blur(10px)';
    } else {
        header.style.background = 'linear-gradient(135deg, var(--deep-blue), var(--dark-blue))';
        header.style.backdropFilter = 'none';
    }
}, 16);

window.addEventListener('scroll', throttledScrollHandler);

// Multimedia Page Tab Functionality
document.addEventListener('DOMContentLoaded', function() {
    const tabButtons = document.querySelectorAll('.tab-btn');
    const mediaCards = document.querySelectorAll('.media-card');

    if (tabButtons.length > 0 && mediaCards.length > 0) {
        tabButtons.forEach(button => {
            button.addEventListener('click', function() {
                const category = this.getAttribute('data-category');

                // Update active tab
                tabButtons.forEach(btn => btn.classList.remove('active'));
                this.classList.add('active');

                // Filter media cards
                mediaCards.forEach(card => {
                    if (category === 'all' || card.classList.contains(category)) {
                        card.style.display = 'block';
                        card.style.animation = 'fadeInUp 0.5s ease-out';
                    } else {
                        card.style.display = 'none';
                    }
                });
            });
        });
    }
});

// Audio Controls for Duas and Ziyarat
document.addEventListener('DOMContentLoaded', function() {
    const playButtons = document.querySelectorAll('.play-btn');
    let currentAudio = null;

    playButtons.forEach(button => {
        button.addEventListener('click', function() {
            const audioId = this.getAttribute('data-audio');

            // Stop current audio if playing
            if (currentAudio && !currentAudio.paused) {
                currentAudio.pause();
                currentAudio.currentTime = 0;
                // Reset all play buttons
                playButtons.forEach(btn => btn.textContent = '🔊');
            }

            // For demo purposes, we'll simulate audio playback
            // In a real implementation, you would load actual audio files
            if (this.textContent === '🔊') {
                this.textContent = '⏸️';
                console.log(`Playing audio: ${audioId}`);

                // Simulate audio duration countdown
                let duration = parseInt(this.nextElementSibling.textContent.split(':')[0]) * 60 +
                              parseInt(this.nextElementSibling.textContent.split(':')[1]);

                // Create a mock audio object for demonstration
                currentAudio = {
                    paused: false,
                    currentTime: 0,
                    pause: () => {
                        this.textContent = '🔊';
                        console.log(`Paused audio: ${audioId}`);
                    }
                };

                // Auto-reset after a few seconds (for demo)
                setTimeout(() => {
                    if (currentAudio && !currentAudio.paused) {
                        this.textContent = '🔊';
                        currentAudio = null;
                    }
                }, 5000);
            } else {
                this.textContent = '🔊';
                if (currentAudio) {
                    currentAudio.pause();
                    currentAudio = null;
                }
            }
        });
    });
});

// Video Player Functionality
document.addEventListener('DOMContentLoaded', function() {
    const videoCards = document.querySelectorAll('.video-card');

    videoCards.forEach(card => {
        const overlay = card.querySelector('.video-overlay');
        const iframe = card.querySelector('iframe');

        if (overlay && iframe) {
            overlay.addEventListener('click', function() {
                // Hide overlay when video is clicked
                this.style.display = 'none';

                // Enable iframe interaction
                iframe.style.pointerEvents = 'auto';
            });
        }
    });
});

// Enhanced Tab Functionality for All Pages
document.addEventListener('DOMContentLoaded', function() {
    const allTabButtons = document.querySelectorAll('.tab-btn');

    allTabButtons.forEach(button => {
        button.addEventListener('click', function() {
            const category = this.getAttribute('data-category');
            const parentSection = this.closest('section');
            const tabButtons = parentSection.querySelectorAll('.tab-btn');
            const items = parentSection.querySelectorAll('[class*="-card"]');

            // Update active tab
            tabButtons.forEach(btn => btn.classList.remove('active'));
            this.classList.add('active');

            // Filter items
            items.forEach(item => {
                const itemClasses = item.className;
                if (category === 'all' || itemClasses.includes(category)) {
                    item.style.display = 'block';
                    item.style.animation = 'fadeInUp 0.5s ease-out';
                } else {
                    item.style.display = 'none';
                }
            });
        });
    });
});
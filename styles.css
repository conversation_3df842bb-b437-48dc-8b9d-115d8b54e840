/* CSS Variables for Color Palette */
:root {
    --deep-blue: #1e3a8a;
    --emerald-green: #059669;
    --warm-gold: #d97706;
    --soft-cream: #fef7ed;
    --light-cream: #fffbf0;
    --dark-blue: #1e40af;
    --light-green: #10b981;
    --gold-accent: #f59e0b;
    --text-dark: #1f2937;
    --text-light: #6b7280;
    --white: #ffffff;
}

/* Reset and Base Styles */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Inter', sans-serif;
    line-height: 1.6;
    color: var(--text-dark);
    background-color: var(--soft-cream);
}

.container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 20px;
}

/* Header Styles */
.header {
    background: linear-gradient(135deg, var(--deep-blue), var(--dark-blue));
    box-shadow: 0 4px 20px rgba(30, 58, 138, 0.3);
    position: fixed;
    top: 0;
    width: 100%;
    z-index: 1000;
}

.nav-container {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 1rem 2rem;
    max-width: 1200px;
    margin: 0 auto;
}

.logo-text {
    font-family: 'Amiri', serif;
    font-size: 1.8rem;
    color: var(--warm-gold);
    margin-bottom: 0.2rem;
}

.logo-subtitle {
    font-size: 0.9rem;
    color: var(--soft-cream);
    display: block;
}

.nav-menu {
    display: flex;
    list-style: none;
    gap: 2rem;
}

.nav-link {
    color: var(--soft-cream);
    text-decoration: none;
    font-weight: 500;
    transition: all 0.3s ease;
    position: relative;
}

.nav-link:hover,
.nav-link.active {
    color: var(--warm-gold);
}

.nav-link::after {
    content: '';
    position: absolute;
    bottom: -5px;
    left: 0;
    width: 0;
    height: 2px;
    background: var(--warm-gold);
    transition: width 0.3s ease;
}

.nav-link:hover::after,
.nav-link.active::after {
    width: 100%;
}

/* Hamburger Menu */
.hamburger {
    display: none;
    flex-direction: column;
    cursor: pointer;
}

.hamburger span {
    width: 25px;
    height: 3px;
    background: var(--soft-cream);
    margin: 3px 0;
    transition: 0.3s;
}

/* Hero Section */
.hero {
    background: linear-gradient(135deg, var(--deep-blue), var(--emerald-green));
    min-height: 100vh;
    display: flex;
    align-items: center;
    position: relative;
    overflow: hidden;
    margin-top: 80px;
}

.geometric-pattern {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    opacity: 0.1;
    background-image:
        radial-gradient(circle at 25% 25%, var(--warm-gold) 2px, transparent 2px),
        radial-gradient(circle at 75% 75%, var(--warm-gold) 2px, transparent 2px);
    background-size: 60px 60px;
    background-position: 0 0, 30px 30px;
}

.hero-content {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 4rem;
    align-items: center;
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 2rem;
    position: relative;
    z-index: 2;
}

.hero-title {
    font-family: 'Amiri', serif;
    font-size: 2.5rem;
    color: var(--warm-gold);
    margin-bottom: 1rem;
    text-align: center;
}

.hero-subtitle {
    font-size: 2.2rem;
    color: var(--soft-cream);
    margin-bottom: 1rem;
    font-weight: 600;
}

.hero-description {
    font-size: 1.1rem;
    color: var(--light-cream);
    margin-bottom: 2rem;
    line-height: 1.8;
}

.hero-buttons {
    display: flex;
    gap: 1rem;
    flex-wrap: wrap;
}

.btn {
    padding: 12px 24px;
    border-radius: 8px;
    text-decoration: none;
    font-weight: 600;
    transition: all 0.3s ease;
    display: inline-block;
}

.btn-primary {
    background: var(--warm-gold);
    color: var(--deep-blue);
}

.btn-primary:hover {
    background: var(--gold-accent);
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(217, 119, 6, 0.3);
}

.btn-secondary {
    background: transparent;
    color: var(--soft-cream);
    border: 2px solid var(--soft-cream);
}

.btn-secondary:hover {
    background: var(--soft-cream);
    color: var(--deep-blue);
    transform: translateY(-2px);
}

/* Shrine Silhouette */
.shrine-silhouette {
    width: 300px;
    height: 400px;
    background: linear-gradient(45deg, var(--warm-gold), var(--gold-accent));
    margin: 0 auto;
    position: relative;
    border-radius: 20px 20px 0 0;
    opacity: 0.8;
}

.shrine-silhouette::before {
    content: '';
    position: absolute;
    top: -30px;
    left: 50%;
    transform: translateX(-50%);
    width: 60px;
    height: 60px;
    background: var(--warm-gold);
    border-radius: 50%;
}

.shrine-silhouette::after {
    content: '';
    position: absolute;
    top: -15px;
    left: 50%;
    transform: translateX(-50%);
    width: 20px;
    height: 40px;
    background: var(--warm-gold);
    border-radius: 10px 10px 0 0;
}

/* Main Sections */
.main-sections {
    padding: 6rem 0;
    background: var(--light-cream);
}

.section-title {
    text-align: center;
    font-size: 2.5rem;
    color: var(--deep-blue);
    margin-bottom: 3rem;
    font-weight: 700;
}

.sections-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
    gap: 2rem;
    margin-top: 3rem;
}

.section-card {
    background: var(--white);
    padding: 2rem;
    border-radius: 15px;
    text-align: center;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
    transition: all 0.3s ease;
    border-top: 4px solid var(--emerald-green);
}

.section-card:hover {
    transform: translateY(-10px);
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.15);
}

.card-icon {
    width: 80px;
    height: 80px;
    margin: 0 auto 1.5rem;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    position: relative;
}

.education-icon {
    background: linear-gradient(135deg, var(--emerald-green), var(--light-green));
}

.community-icon {
    background: linear-gradient(135deg, var(--deep-blue), var(--dark-blue));
}

.multimedia-icon {
    background: linear-gradient(135deg, var(--warm-gold), var(--gold-accent));
}

.duas-icon {
    background: linear-gradient(135deg, var(--warm-gold), var(--emerald-green));
}

.ziyarat-icon {
    background: linear-gradient(135deg, var(--deep-blue), var(--warm-gold));
}

.videos-icon {
    background: linear-gradient(135deg, var(--emerald-green), var(--warm-gold));
}

.about-icon {
    background: linear-gradient(135deg, var(--emerald-green), var(--deep-blue));
}

.section-card h3 {
    font-size: 1.5rem;
    color: var(--deep-blue);
    margin-bottom: 1rem;
    font-weight: 600;
}

.section-card p {
    color: var(--text-light);
    margin-bottom: 1.5rem;
    line-height: 1.7;
}

.card-link {
    color: var(--emerald-green);
    text-decoration: none;
    font-weight: 600;
    transition: color 0.3s ease;
}

.card-link:hover {
    color: var(--deep-blue);
}

/* Quote Section */
.quote-section {
    background: linear-gradient(135deg, var(--deep-blue), var(--emerald-green));
    padding: 4rem 0;
    position: relative;
}

.quote-section::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-image:
        repeating-linear-gradient(
            45deg,
            transparent,
            transparent 10px,
            rgba(217, 119, 6, 0.1) 10px,
            rgba(217, 119, 6, 0.1) 20px
        );
}

.quote-content {
    position: relative;
    z-index: 2;
    text-align: center;
    max-width: 800px;
    margin: 0 auto;
}

.islamic-quote {
    background: rgba(255, 255, 255, 0.1);
    padding: 3rem;
    border-radius: 20px;
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.2);
}

.quote-arabic {
    font-family: 'Amiri', serif;
    font-size: 1.8rem;
    color: var(--warm-gold);
    margin-bottom: 1.5rem;
    line-height: 1.8;
}

.quote-translation {
    font-size: 1.2rem;
    color: var(--soft-cream);
    margin-bottom: 1rem;
    font-style: italic;
    line-height: 1.6;
}

.quote-source {
    color: var(--light-cream);
    font-size: 1rem;
    font-weight: 500;
}

/* Footer */
.footer {
    background: var(--text-dark);
    color: var(--soft-cream);
    padding: 3rem 0 1rem;
}

.footer-content {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 2rem;
    margin-bottom: 2rem;
}

.footer-section h3,
.footer-section h4 {
    color: var(--warm-gold);
    margin-bottom: 1rem;
}

.footer-section ul {
    list-style: none;
}

.footer-section ul li {
    margin-bottom: 0.5rem;
}

.footer-section a {
    color: var(--light-cream);
    text-decoration: none;
    transition: color 0.3s ease;
}

.footer-section a:hover {
    color: var(--warm-gold);
}

.footer-bottom {
    border-top: 1px solid rgba(254, 247, 237, 0.2);
    padding-top: 1rem;
    text-align: center;
    color: var(--text-light);
}

/* Responsive Design */
@media (max-width: 768px) {
    .hamburger {
        display: flex;
    }

    .nav-menu {
        position: fixed;
        top: 80px;
        left: -100%;
        width: 100%;
        height: calc(100vh - 80px);
        background: var(--deep-blue);
        flex-direction: column;
        justify-content: flex-start;
        align-items: center;
        padding-top: 2rem;
        transition: left 0.3s ease;
    }

    .nav-menu.active {
        left: 0;
    }

    .nav-menu li {
        margin: 1rem 0;
    }

    .hero-content {
        grid-template-columns: 1fr;
        text-align: center;
        gap: 2rem;
    }

    .hero-title {
        font-size: 2rem;
    }

    .hero-subtitle {
        font-size: 1.8rem;
    }

    .shrine-silhouette {
        width: 200px;
        height: 280px;
    }

    .sections-grid {
        grid-template-columns: 1fr;
    }

    .quote-arabic {
        font-size: 1.4rem;
    }

    .quote-translation {
        font-size: 1rem;
    }
}

@media (max-width: 480px) {
    .hero-buttons {
        flex-direction: column;
        align-items: center;
    }

    .btn {
        width: 100%;
        max-width: 250px;
        text-align: center;
    }

    .section-card {
        padding: 1.5rem;
    }

    .islamic-quote {
        padding: 2rem;
    }
}

/* Animation Classes */
@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.fade-in-up {
    animation: fadeInUp 0.6s ease-out;
}

/* Scroll Animation */
[data-aos] {
    opacity: 0;
    transform: translateY(30px);
    transition: all 0.6s ease;
}

[data-aos].aos-animate {
    opacity: 1;
    transform: translateY(0);
}

/* Page Header Styles */
.page-header {
    background: linear-gradient(135deg, var(--deep-blue), var(--emerald-green));
    padding: 8rem 0 4rem;
    text-align: center;
    margin-top: 80px;
    position: relative;
}

.page-header::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-image:
        radial-gradient(circle at 20% 80%, rgba(217, 119, 6, 0.1) 0%, transparent 50%),
        radial-gradient(circle at 80% 20%, rgba(217, 119, 6, 0.1) 0%, transparent 50%);
}

.page-title {
    font-size: 3rem;
    color: var(--warm-gold);
    margin-bottom: 1rem;
    font-weight: 700;
    position: relative;
    z-index: 2;
}

.page-subtitle {
    font-size: 1.2rem;
    color: var(--soft-cream);
    max-width: 600px;
    margin: 0 auto;
    position: relative;
    z-index: 2;
}

/* Education Categories */
.education-categories {
    padding: 6rem 0;
    background: var(--light-cream);
}

.categories-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
    gap: 2rem;
}

.category-card {
    background: var(--white);
    padding: 2.5rem;
    border-radius: 15px;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
    transition: all 0.3s ease;
    border-left: 5px solid var(--emerald-green);
}

.category-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.15);
}

.category-icon {
    width: 70px;
    height: 70px;
    border-radius: 50%;
    margin-bottom: 1.5rem;
    position: relative;
}

.quran-icon {
    background: linear-gradient(135deg, var(--emerald-green), var(--light-green));
}

.hadith-icon {
    background: linear-gradient(135deg, var(--warm-gold), var(--gold-accent));
}

.fiqh-icon {
    background: linear-gradient(135deg, var(--deep-blue), var(--dark-blue));
}

.history-icon {
    background: linear-gradient(135deg, var(--emerald-green), var(--deep-blue));
}

.theology-icon {
    background: linear-gradient(135deg, var(--warm-gold), var(--emerald-green));
}

.arabic-icon {
    background: linear-gradient(135deg, var(--deep-blue), var(--warm-gold));
}

.category-card h3 {
    font-size: 1.5rem;
    color: var(--deep-blue);
    margin-bottom: 1rem;
    font-weight: 600;
}

.category-card p {
    color: var(--text-light);
    margin-bottom: 1.5rem;
    line-height: 1.7;
}

.resource-list {
    list-style: none;
    margin-bottom: 2rem;
}

.resource-list li {
    padding: 0.5rem 0;
    color: var(--text-dark);
    position: relative;
    padding-left: 1.5rem;
}

.resource-list li::before {
    content: '•';
    color: var(--emerald-green);
    font-weight: bold;
    position: absolute;
    left: 0;
}

/* Online Courses */
.online-courses {
    padding: 6rem 0;
    background: var(--soft-cream);
}

.courses-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 2rem;
    margin-top: 3rem;
}

.course-card {
    background: var(--white);
    border-radius: 15px;
    overflow: hidden;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
    transition: all 0.3s ease;
}

.course-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.15);
}

.course-image {
    height: 200px;
    background: linear-gradient(135deg, var(--deep-blue), var(--emerald-green));
    position: relative;
}

.course-image::after {
    content: '📚';
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    font-size: 3rem;
    opacity: 0.7;
}

.course-content {
    padding: 2rem;
}

.course-content h3 {
    font-size: 1.3rem;
    color: var(--deep-blue);
    margin-bottom: 0.5rem;
    font-weight: 600;
}

.course-instructor {
    color: var(--emerald-green);
    font-size: 0.9rem;
    margin-bottom: 1rem;
    font-weight: 500;
}

.course-description {
    color: var(--text-light);
    margin-bottom: 1.5rem;
    line-height: 1.6;
}

.course-meta {
    display: flex;
    gap: 1rem;
    margin-bottom: 1.5rem;
}

.course-duration,
.course-level {
    background: var(--light-cream);
    padding: 0.3rem 0.8rem;
    border-radius: 20px;
    font-size: 0.8rem;
    color: var(--deep-blue);
    font-weight: 500;
}

/* Digital Library */
.digital-library {
    padding: 6rem 0;
    background: linear-gradient(135deg, var(--deep-blue), var(--emerald-green));
    text-align: center;
    color: var(--soft-cream);
}

.digital-library .section-title {
    color: var(--warm-gold);
}

.library-description {
    font-size: 1.1rem;
    max-width: 600px;
    margin: 0 auto 3rem;
    line-height: 1.7;
}

.library-stats {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
    gap: 2rem;
    margin-bottom: 3rem;
}

.stat-item h3 {
    font-size: 2.5rem;
    color: var(--warm-gold);
    margin-bottom: 0.5rem;
    font-weight: 700;
}

.stat-item p {
    color: var(--light-cream);
    font-size: 1rem;
}

.library-btn {
    font-size: 1.1rem;
    padding: 15px 30px;
}

/* Community Page Styles */
.community-features {
    padding: 6rem 0;
    background: var(--light-cream);
}

.features-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
    gap: 2rem;
}

.feature-card {
    background: var(--white);
    padding: 2.5rem;
    border-radius: 15px;
    text-align: center;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
    transition: all 0.3s ease;
    border-top: 4px solid var(--emerald-green);
}

.feature-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.15);
}

.feature-icon {
    width: 80px;
    height: 80px;
    margin: 0 auto 1.5rem;
    border-radius: 50%;
    position: relative;
}

.forum-icon {
    background: linear-gradient(135deg, var(--deep-blue), var(--dark-blue));
}

.centers-icon {
    background: linear-gradient(135deg, var(--emerald-green), var(--light-green));
}

.events-icon {
    background: linear-gradient(135deg, var(--warm-gold), var(--gold-accent));
}

.support-icon {
    background: linear-gradient(135deg, var(--emerald-green), var(--deep-blue));
}

.feature-stats {
    display: flex;
    justify-content: space-between;
    margin: 1.5rem 0;
    font-size: 0.9rem;
    color: var(--emerald-green);
    font-weight: 500;
}

/* Events Styles */
.upcoming-events {
    padding: 6rem 0;
    background: var(--soft-cream);
}

.events-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
    gap: 2rem;
    margin-top: 3rem;
}

.event-card {
    background: var(--white);
    border-radius: 15px;
    padding: 2rem;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
    display: flex;
    gap: 1.5rem;
    transition: all 0.3s ease;
}

.event-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.15);
}

.event-date {
    background: var(--emerald-green);
    color: var(--white);
    padding: 1rem;
    border-radius: 10px;
    text-align: center;
    min-width: 80px;
    height: fit-content;
}

.event-date .day {
    display: block;
    font-size: 1.8rem;
    font-weight: 700;
}

.event-date .month {
    display: block;
    font-size: 0.9rem;
    text-transform: uppercase;
}

.event-content h3 {
    color: var(--deep-blue);
    margin-bottom: 0.5rem;
    font-weight: 600;
}

.event-location,
.event-time {
    color: var(--text-light);
    font-size: 0.9rem;
    margin-bottom: 0.5rem;
}

.event-description {
    color: var(--text-dark);
    margin: 1rem 0;
    line-height: 1.6;
}

.event-link {
    color: var(--emerald-green);
    text-decoration: none;
    font-weight: 600;
    transition: color 0.3s ease;
}

.event-link:hover {
    color: var(--deep-blue);
}

/* Community Guidelines */
.community-guidelines {
    padding: 6rem 0;
    background: var(--light-cream);
}

.guidelines-content {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 2rem;
    margin-top: 3rem;
}

.guideline-item {
    text-align: center;
    padding: 2rem;
}

.guideline-icon {
    width: 70px;
    height: 70px;
    margin: 0 auto 1.5rem;
    border-radius: 50%;
}

.respect-icon {
    background: linear-gradient(135deg, var(--emerald-green), var(--light-green));
}

.knowledge-icon {
    background: linear-gradient(135deg, var(--warm-gold), var(--gold-accent));
}

.unity-icon {
    background: linear-gradient(135deg, var(--deep-blue), var(--dark-blue));
}

.privacy-icon {
    background: linear-gradient(135deg, var(--emerald-green), var(--deep-blue));
}

/* Community Stats */
.community-stats {
    padding: 6rem 0;
    background: linear-gradient(135deg, var(--deep-blue), var(--emerald-green));
    text-align: center;
}

.community-stats .section-title {
    color: var(--warm-gold);
}

.stats-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 2rem;
    margin-top: 3rem;
}

.stat-card {
    background: rgba(255, 255, 255, 0.1);
    padding: 2rem;
    border-radius: 15px;
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.2);
}

.stat-number {
    font-size: 2.5rem;
    font-weight: 700;
    color: var(--warm-gold);
    margin-bottom: 0.5rem;
}

.stat-label {
    color: var(--soft-cream);
    font-size: 1.1rem;
    font-weight: 500;
}

/* Join Community CTA */
.join-community {
    padding: 6rem 0;
    background: var(--soft-cream);
    text-align: center;
}

.cta-content h2 {
    color: var(--deep-blue);
    font-size: 2.5rem;
    margin-bottom: 1rem;
}

.cta-content p {
    color: var(--text-light);
    font-size: 1.1rem;
    max-width: 600px;
    margin: 0 auto 2rem;
    line-height: 1.7;
}

.cta-buttons {
    display: flex;
    gap: 1rem;
    justify-content: center;
    flex-wrap: wrap;
}

/* Multimedia Page Styles */
.multimedia-categories {
    padding: 6rem 0;
    background: var(--light-cream);
}

.category-tabs {
    display: flex;
    justify-content: center;
    gap: 1rem;
    margin-bottom: 3rem;
    flex-wrap: wrap;
}

.tab-btn {
    padding: 0.8rem 1.5rem;
    border: 2px solid var(--emerald-green);
    background: transparent;
    color: var(--emerald-green);
    border-radius: 25px;
    cursor: pointer;
    transition: all 0.3s ease;
    font-weight: 500;
}

.tab-btn.active,
.tab-btn:hover {
    background: var(--emerald-green);
    color: var(--white);
}

.multimedia-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 2rem;
}

.media-card {
    background: var(--white);
    border-radius: 15px;
    overflow: hidden;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
    transition: all 0.3s ease;
}

.media-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.15);
}

.media-thumbnail {
    height: 200px;
    background: linear-gradient(135deg, var(--deep-blue), var(--emerald-green));
    position: relative;
    display: flex;
    align-items: center;
    justify-content: center;
}

.play-button {
    width: 60px;
    height: 60px;
    background: rgba(255, 255, 255, 0.9);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.5rem;
    color: var(--deep-blue);
    cursor: pointer;
    transition: all 0.3s ease;
}

.play-button:hover {
    background: var(--white);
    transform: scale(1.1);
}

.play-button.large {
    width: 80px;
    height: 80px;
    font-size: 2rem;
}

.media-type {
    position: absolute;
    top: 10px;
    right: 10px;
    background: rgba(0, 0, 0, 0.7);
    color: var(--white);
    padding: 0.3rem 0.8rem;
    border-radius: 15px;
    font-size: 0.8rem;
}

.media-content {
    padding: 1.5rem;
}

.media-content h3 {
    color: var(--deep-blue);
    margin-bottom: 0.5rem;
    font-weight: 600;
}

.media-author {
    color: var(--emerald-green);
    font-size: 0.9rem;
    margin-bottom: 0.8rem;
    font-weight: 500;
}

.media-description {
    color: var(--text-light);
    margin-bottom: 1rem;
    line-height: 1.6;
    font-size: 0.9rem;
}

.media-meta {
    display: flex;
    justify-content: space-between;
    font-size: 0.8rem;
    color: var(--text-light);
}

/* Featured Content */
.featured-content {
    padding: 6rem 0;
    background: var(--soft-cream);
}

.featured-grid {
    display: grid;
    grid-template-columns: 2fr 1fr;
    gap: 3rem;
    margin-top: 3rem;
}

.featured-main {
    background: var(--white);
    border-radius: 15px;
    overflow: hidden;
    box-shadow: 0 15px 40px rgba(0, 0, 0, 0.1);
}

.featured-thumbnail {
    height: 300px;
    background: linear-gradient(135deg, var(--deep-blue), var(--emerald-green));
    position: relative;
    display: flex;
    align-items: center;
    justify-content: center;
}

.featured-badge {
    position: absolute;
    top: 15px;
    left: 15px;
    background: var(--warm-gold);
    color: var(--white);
    padding: 0.5rem 1rem;
    border-radius: 20px;
    font-size: 0.8rem;
    font-weight: 600;
}

.featured-info {
    padding: 2rem;
}

.featured-info h3 {
    color: var(--deep-blue);
    font-size: 1.5rem;
    margin-bottom: 0.5rem;
}

.featured-author {
    color: var(--emerald-green);
    margin-bottom: 1rem;
    font-weight: 500;
}

.featured-description {
    color: var(--text-dark);
    line-height: 1.7;
    margin-bottom: 1.5rem;
}

.featured-meta {
    display: flex;
    gap: 1rem;
    margin-bottom: 2rem;
    font-size: 0.9rem;
    color: var(--text-light);
}

.featured-sidebar h4 {
    color: var(--deep-blue);
    margin-bottom: 1.5rem;
    font-size: 1.2rem;
}

.related-item {
    display: flex;
    gap: 1rem;
    margin-bottom: 1.5rem;
    padding: 1rem;
    background: var(--white);
    border-radius: 10px;
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
}

.related-thumbnail {
    width: 80px;
    height: 60px;
    background: linear-gradient(135deg, var(--emerald-green), var(--deep-blue));
    border-radius: 8px;
    flex-shrink: 0;
}

.related-info h5 {
    color: var(--deep-blue);
    margin-bottom: 0.3rem;
    font-size: 0.9rem;
}

.related-duration {
    color: var(--text-light);
    font-size: 0.8rem;
}

/* Live Streams */
.live-streams {
    padding: 6rem 0;
    background: var(--light-cream);
}

.streams-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 2rem;
    margin-top: 3rem;
}

.stream-card {
    background: var(--white);
    border-radius: 15px;
    overflow: hidden;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
    transition: all 0.3s ease;
}

.stream-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.15);
}

.stream-status {
    position: absolute;
    top: 10px;
    left: 10px;
    padding: 0.3rem 0.8rem;
    border-radius: 15px;
    font-size: 0.8rem;
    font-weight: 600;
    z-index: 2;
}

.stream-card.live .stream-status {
    background: #ff4444;
    color: var(--white);
}

.stream-card.upcoming .stream-status {
    background: var(--warm-gold);
    color: var(--white);
}

.stream-thumbnail {
    height: 180px;
    background: linear-gradient(135deg, var(--deep-blue), var(--emerald-green));
    position: relative;
    display: flex;
    align-items: center;
    justify-content: center;
}

.viewer-count,
.start-time {
    position: absolute;
    bottom: 10px;
    right: 10px;
    background: rgba(0, 0, 0, 0.7);
    color: var(--white);
    padding: 0.3rem 0.8rem;
    border-radius: 15px;
    font-size: 0.8rem;
}

.stream-info {
    padding: 1.5rem;
}

.stream-info h3 {
    color: var(--deep-blue);
    margin-bottom: 0.5rem;
    font-weight: 600;
}

.stream-location,
.stream-time {
    color: var(--text-light);
    font-size: 0.9rem;
    margin-bottom: 0.3rem;
}

/* About Page Styles */
.mission-statement {
    padding: 6rem 0;
    background: var(--light-cream);
}

.mission-content {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 4rem;
    align-items: center;
}

.mission-text h2 {
    color: var(--deep-blue);
    font-size: 2.5rem;
    margin-bottom: 1.5rem;
    font-weight: 700;
}

.mission-description {
    color: var(--text-dark);
    font-size: 1.1rem;
    line-height: 1.8;
    margin-bottom: 2rem;
}

.mission-values {
    display: flex;
    flex-direction: column;
    gap: 1.5rem;
}

.value-item h4 {
    color: var(--emerald-green);
    margin-bottom: 0.5rem;
    font-size: 1.1rem;
}

.value-item p {
    color: var(--text-light);
    line-height: 1.6;
}

.mission-visual {
    text-align: center;
}

.islamic-calligraphy {
    background: var(--white);
    padding: 3rem;
    border-radius: 20px;
    box-shadow: 0 15px 40px rgba(0, 0, 0, 0.1);
    border: 3px solid var(--warm-gold);
}

.arabic-text {
    font-family: 'Amiri', serif;
    font-size: 1.8rem;
    color: var(--deep-blue);
    margin-bottom: 1rem;
    line-height: 1.8;
}

.translation {
    font-size: 1rem;
    color: var(--text-dark);
    margin-bottom: 1rem;
    font-style: italic;
    line-height: 1.6;
}

.islamic-calligraphy cite {
    color: var(--emerald-green);
    font-size: 0.9rem;
    font-weight: 500;
}

/* Our Story Timeline */
.our-story {
    padding: 6rem 0;
    background: var(--soft-cream);
}

.story-timeline {
    position: relative;
    max-width: 800px;
    margin: 3rem auto 0;
}

.story-timeline::before {
    content: '';
    position: absolute;
    left: 50%;
    top: 0;
    bottom: 0;
    width: 3px;
    background: var(--emerald-green);
    transform: translateX(-50%);
}

.timeline-item {
    display: flex;
    align-items: center;
    margin-bottom: 3rem;
    position: relative;
}

.timeline-item:nth-child(odd) {
    flex-direction: row;
}

.timeline-item:nth-child(even) {
    flex-direction: row-reverse;
}

.timeline-year {
    background: var(--emerald-green);
    color: var(--white);
    padding: 1rem;
    border-radius: 50%;
    font-weight: 700;
    font-size: 1.1rem;
    min-width: 80px;
    text-align: center;
    position: relative;
    z-index: 2;
}

.timeline-content {
    background: var(--white);
    padding: 2rem;
    border-radius: 15px;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
    flex: 1;
    margin: 0 2rem;
}

.timeline-content h3 {
    color: var(--deep-blue);
    margin-bottom: 0.8rem;
    font-weight: 600;
}

.timeline-content p {
    color: var(--text-light);
    line-height: 1.6;
}

/* Our Team */
.our-team {
    padding: 6rem 0;
    background: var(--light-cream);
}

.team-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 2rem;
    margin-top: 3rem;
}

.team-member {
    background: var(--white);
    padding: 2rem;
    border-radius: 15px;
    text-align: center;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
    transition: all 0.3s ease;
}

.team-member:hover {
    transform: translateY(-5px);
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.15);
}

.member-photo {
    width: 120px;
    height: 120px;
    background: linear-gradient(135deg, var(--emerald-green), var(--deep-blue));
    border-radius: 50%;
    margin: 0 auto 1.5rem;
    position: relative;
}

.member-photo::after {
    content: '👤';
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    font-size: 3rem;
    opacity: 0.7;
}

.team-member h3 {
    color: var(--deep-blue);
    margin-bottom: 0.5rem;
    font-weight: 600;
}

.member-title {
    color: var(--emerald-green);
    font-size: 0.9rem;
    margin-bottom: 1rem;
    font-weight: 500;
}

.member-bio {
    color: var(--text-light);
    line-height: 1.6;
    font-size: 0.9rem;
}

/* Our Impact */
.our-impact {
    padding: 6rem 0;
    background: linear-gradient(135deg, var(--deep-blue), var(--emerald-green));
    text-align: center;
}

.our-impact .section-title {
    color: var(--warm-gold);
}

.impact-stats {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 2rem;
    margin-top: 3rem;
}

.impact-item {
    background: rgba(255, 255, 255, 0.1);
    padding: 2.5rem;
    border-radius: 15px;
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.2);
}

.impact-number {
    font-size: 3rem;
    font-weight: 700;
    color: var(--warm-gold);
    margin-bottom: 0.5rem;
}

.impact-label {
    color: var(--soft-cream);
    font-size: 1.2rem;
    font-weight: 600;
    margin-bottom: 1rem;
}

.impact-item p {
    color: var(--light-cream);
    line-height: 1.6;
}

/* Contact Information */
.contact-info {
    padding: 6rem 0;
    background: var(--soft-cream);
}

.contact-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 2rem;
    margin-top: 3rem;
}

.contact-item {
    background: var(--white);
    padding: 2rem;
    border-radius: 15px;
    text-align: center;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
    transition: all 0.3s ease;
}

.contact-item:hover {
    transform: translateY(-5px);
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.15);
}

.contact-icon {
    width: 70px;
    height: 70px;
    margin: 0 auto 1.5rem;
    border-radius: 50%;
}

.email-icon {
    background: linear-gradient(135deg, var(--emerald-green), var(--light-green));
}

.phone-icon {
    background: linear-gradient(135deg, var(--warm-gold), var(--gold-accent));
}

.location-icon {
    background: linear-gradient(135deg, var(--deep-blue), var(--dark-blue));
}

.social-icon {
    background: linear-gradient(135deg, var(--emerald-green), var(--deep-blue));
}

.contact-item h3 {
    color: var(--deep-blue);
    margin-bottom: 1rem;
    font-weight: 600;
}

.contact-item p {
    color: var(--text-light);
    margin-bottom: 0.5rem;
    line-height: 1.6;
}

/* Additional Responsive Styles */
@media (max-width: 768px) {
    .page-title {
        font-size: 2.2rem;
    }

    .categories-grid,
    .features-grid {
        grid-template-columns: 1fr;
    }

    .featured-grid {
        grid-template-columns: 1fr;
    }

    .mission-content {
        grid-template-columns: 1fr;
        gap: 2rem;
    }

    .timeline-item {
        flex-direction: column !important;
        text-align: center;
    }

    .timeline-content {
        margin: 1rem 0;
    }

    .story-timeline::before {
        display: none;
    }

    .arabic-text {
        font-size: 1.4rem;
    }
}

/* Duas Page Styles */
.duas-categories {
    padding: 6rem 0;
    background: var(--light-cream);
}

.duas-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
    gap: 2rem;
    margin-top: 3rem;
}

.dua-card {
    background: var(--white);
    border-radius: 15px;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
    transition: all 0.3s ease;
    border-top: 4px solid var(--emerald-green);
    overflow: hidden;
}

.dua-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.15);
}

.dua-header {
    background: linear-gradient(135deg, var(--deep-blue), var(--emerald-green));
    color: var(--white);
    padding: 1.5rem;
    position: relative;
}

.dua-header h3 {
    font-family: 'Amiri', serif;
    font-size: 1.8rem;
    margin-bottom: 0.5rem;
    text-align: center;
}

.dua-title {
    display: block;
    text-align: center;
    font-size: 1.1rem;
    margin-bottom: 1rem;
    color: var(--soft-cream);
}

.audio-controls {
    display: flex;
    justify-content: center;
    align-items: center;
    gap: 1rem;
}

.play-btn {
    background: var(--warm-gold);
    border: none;
    border-radius: 50%;
    width: 40px;
    height: 40px;
    font-size: 1.2rem;
    cursor: pointer;
    transition: all 0.3s ease;
}

.play-btn:hover {
    background: var(--gold-accent);
    transform: scale(1.1);
}

.duration {
    color: var(--light-cream);
    font-size: 0.9rem;
}

.dua-content {
    padding: 2rem;
}

.arabic-text {
    font-family: 'Amiri', serif;
    font-size: 1.4rem;
    line-height: 2;
    color: var(--deep-blue);
    text-align: right;
    margin-bottom: 1.5rem;
    padding: 1rem;
    background: var(--light-cream);
    border-radius: 10px;
    border-right: 4px solid var(--warm-gold);
}

.translation {
    font-size: 1rem;
    line-height: 1.7;
    color: var(--text-dark);
    margin-bottom: 1.5rem;
    font-style: italic;
}

.dua-benefits,
.dua-time {
    margin-bottom: 1rem;
    font-size: 0.9rem;
    color: var(--text-light);
}

.dua-benefits strong,
.dua-time strong {
    color: var(--emerald-green);
}

/* Featured Duas Collection */
.featured-duas {
    padding: 6rem 0;
    background: var(--soft-cream);
}

.collection-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 2rem;
    margin-top: 3rem;
}

.collection-item {
    background: var(--white);
    padding: 2rem;
    border-radius: 15px;
    text-align: center;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
    transition: all 0.3s ease;
}

.collection-item:hover {
    transform: translateY(-5px);
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.15);
}

.collection-item h3 {
    color: var(--deep-blue);
    margin-bottom: 1rem;
    font-weight: 600;
}

.collection-item p {
    color: var(--text-light);
    margin-bottom: 1.5rem;
    line-height: 1.6;
}

.duas-count {
    display: inline-block;
    background: var(--emerald-green);
    color: var(--white);
    padding: 0.3rem 1rem;
    border-radius: 20px;
    font-size: 0.8rem;
    margin-bottom: 1.5rem;
}

/* Ziyarat Page Styles */
.ziyarat-categories {
    padding: 6rem 0;
    background: var(--light-cream);
}

.ziyarat-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
    gap: 2rem;
    margin-top: 3rem;
}

.ziyarat-card {
    background: var(--white);
    border-radius: 15px;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
    transition: all 0.3s ease;
    border-top: 4px solid var(--warm-gold);
    overflow: hidden;
}

.ziyarat-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.15);
}

.ziyarat-header {
    background: linear-gradient(135deg, var(--warm-gold), var(--gold-accent));
    color: var(--white);
    padding: 1.5rem;
    position: relative;
}

.ziyarat-header h3 {
    font-family: 'Amiri', serif;
    font-size: 1.8rem;
    margin-bottom: 0.5rem;
    text-align: center;
}

.ziyarat-title {
    display: block;
    text-align: center;
    font-size: 1.1rem;
    margin-bottom: 0.5rem;
    color: var(--light-cream);
}

.ziyarat-for {
    display: block;
    text-align: center;
    font-size: 0.9rem;
    margin-bottom: 1rem;
    color: var(--soft-cream);
    font-style: italic;
}

.ziyarat-content {
    padding: 2rem;
}

.ziyarat-benefits,
.ziyarat-time {
    margin-bottom: 1rem;
    font-size: 0.9rem;
    color: var(--text-light);
}

.ziyarat-benefits strong,
.ziyarat-time strong {
    color: var(--warm-gold);
}

/* Sacred Places */
.sacred-places {
    padding: 6rem 0;
    background: var(--soft-cream);
}

.places-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 2rem;
    margin-top: 3rem;
}

.place-card {
    background: var(--white);
    border-radius: 15px;
    overflow: hidden;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
    transition: all 0.3s ease;
}

.place-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.15);
}

.place-image {
    height: 200px;
    background: linear-gradient(135deg, var(--deep-blue), var(--emerald-green));
    position: relative;
}

.place-image.karbala {
    background: linear-gradient(135deg, var(--warm-gold), var(--gold-accent));
}

.place-image.najaf {
    background: linear-gradient(135deg, var(--emerald-green), var(--light-green));
}

.place-image.mashhad {
    background: linear-gradient(135deg, var(--deep-blue), var(--dark-blue));
}

.place-image.medina {
    background: linear-gradient(135deg, var(--emerald-green), var(--deep-blue));
}

.place-card h3 {
    color: var(--deep-blue);
    margin: 1rem;
    font-weight: 600;
}

.place-card p {
    color: var(--text-light);
    margin: 0 1rem 1rem;
    line-height: 1.6;
}

.place-info {
    display: flex;
    justify-content: space-between;
    margin: 1rem;
    font-size: 0.8rem;
    color: var(--emerald-green);
}

/* Ziyarat Guide */
.ziyarat-guide {
    padding: 6rem 0;
    background: var(--light-cream);
}

.guide-content {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 2rem;
    margin-top: 3rem;
}

.guide-item {
    text-align: center;
    padding: 2rem;
}

.guide-icon {
    width: 70px;
    height: 70px;
    margin: 0 auto 1.5rem;
    border-radius: 50%;
}

.guide-icon.purification {
    background: linear-gradient(135deg, var(--emerald-green), var(--light-green));
}

.guide-icon.direction {
    background: linear-gradient(135deg, var(--warm-gold), var(--gold-accent));
}

.guide-icon.respect {
    background: linear-gradient(135deg, var(--deep-blue), var(--dark-blue));
}

.guide-icon.intention {
    background: linear-gradient(135deg, var(--emerald-green), var(--deep-blue));
}

/* Videos Page Styles */
.video-categories {
    padding: 6rem 0;
    background: var(--light-cream);
}

.videos-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
    gap: 2rem;
    margin-top: 3rem;
}

.video-card {
    background: var(--white);
    border-radius: 15px;
    overflow: hidden;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
    transition: all 0.3s ease;
}

.video-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.15);
}

.video-card.featured {
    grid-column: span 2;
    border: 3px solid var(--warm-gold);
}

.video-thumbnail {
    position: relative;
    height: 200px;
    overflow: hidden;
}

.video-player {
    width: 100%;
    height: 100%;
}

.video-player iframe {
    width: 100%;
    height: 100%;
    border: none;
}

.video-overlay {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.3);
    display: flex;
    align-items: center;
    justify-content: center;
    opacity: 0;
    transition: opacity 0.3s ease;
}

.video-card:hover .video-overlay {
    opacity: 1;
}

.video-duration {
    position: absolute;
    bottom: 10px;
    right: 10px;
    background: rgba(0, 0, 0, 0.8);
    color: var(--white);
    padding: 0.3rem 0.8rem;
    border-radius: 15px;
    font-size: 0.8rem;
}

.featured-badge {
    position: absolute;
    top: 10px;
    left: 10px;
    background: var(--warm-gold);
    color: var(--white);
    padding: 0.3rem 0.8rem;
    border-radius: 15px;
    font-size: 0.8rem;
    font-weight: 600;
}

.live-indicator {
    position: absolute;
    top: 10px;
    left: 10px;
    background: #ff4444;
    color: var(--white);
    padding: 0.3rem 0.8rem;
    border-radius: 15px;
    font-size: 0.8rem;
    font-weight: 600;
}

.viewer-count {
    position: absolute;
    bottom: 10px;
    left: 10px;
    background: rgba(0, 0, 0, 0.8);
    color: var(--white);
    padding: 0.3rem 0.8rem;
    border-radius: 15px;
    font-size: 0.8rem;
}

.video-content {
    padding: 1.5rem;
}

.video-content h3 {
    color: var(--deep-blue);
    margin-bottom: 0.5rem;
    font-weight: 600;
    line-height: 1.4;
}

.video-author {
    color: var(--emerald-green);
    font-size: 0.9rem;
    margin-bottom: 0.8rem;
    font-weight: 500;
}

.video-description {
    color: var(--text-light);
    margin-bottom: 1rem;
    line-height: 1.6;
    font-size: 0.9rem;
}

.video-meta {
    display: flex;
    gap: 1rem;
    margin-bottom: 1rem;
    font-size: 0.8rem;
    color: var(--text-light);
    flex-wrap: wrap;
}

.video-tags {
    display: flex;
    gap: 0.5rem;
    flex-wrap: wrap;
}

.tag {
    background: var(--light-cream);
    color: var(--emerald-green);
    padding: 0.2rem 0.6rem;
    border-radius: 12px;
    font-size: 0.7rem;
    font-weight: 500;
}

/* Video Playlists */
.video-playlists {
    padding: 6rem 0;
    background: var(--soft-cream);
}

.playlists-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
    gap: 2rem;
    margin-top: 3rem;
}

.playlist-card {
    background: var(--white);
    border-radius: 15px;
    overflow: hidden;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
    transition: all 0.3s ease;
}

.playlist-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.15);
}

.playlist-thumbnail {
    height: 150px;
    background: linear-gradient(135deg, var(--deep-blue), var(--emerald-green));
    position: relative;
    display: flex;
    align-items: center;
    justify-content: center;
}

.playlist-count {
    position: absolute;
    top: 10px;
    right: 10px;
    background: rgba(0, 0, 0, 0.8);
    color: var(--white);
    padding: 0.3rem 0.8rem;
    border-radius: 15px;
    font-size: 0.8rem;
}

.play-all-btn {
    background: var(--warm-gold);
    color: var(--white);
    padding: 0.8rem 1.5rem;
    border-radius: 25px;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
}

.play-all-btn:hover {
    background: var(--gold-accent);
    transform: scale(1.05);
}

.playlist-card h3 {
    color: var(--deep-blue);
    margin: 1rem;
    font-weight: 600;
}

.playlist-card p {
    color: var(--text-light);
    margin: 0 1rem 1rem;
    line-height: 1.6;
}

.playlist-meta {
    display: flex;
    justify-content: space-between;
    margin: 1rem;
    font-size: 0.8rem;
    color: var(--emerald-green);
}

/* Live Schedule */
.live-schedule {
    padding: 6rem 0;
    background: var(--light-cream);
}

.schedule-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
    gap: 2rem;
    margin-top: 3rem;
}

.schedule-item {
    background: var(--white);
    border-radius: 15px;
    padding: 2rem;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
    display: flex;
    gap: 1.5rem;
    transition: all 0.3s ease;
}

.schedule-item:hover {
    transform: translateY(-5px);
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.15);
}

.schedule-time {
    background: var(--emerald-green);
    color: var(--white);
    padding: 1rem;
    border-radius: 10px;
    text-align: center;
    min-width: 80px;
    height: fit-content;
}

.schedule-time .day {
    display: block;
    font-size: 1rem;
    font-weight: 600;
    margin-bottom: 0.3rem;
}

.schedule-time .time {
    display: block;
    font-size: 0.8rem;
}

.schedule-content h3 {
    color: var(--deep-blue);
    margin-bottom: 0.5rem;
    font-weight: 600;
}

.schedule-speaker {
    color: var(--emerald-green);
    font-size: 0.9rem;
    margin-bottom: 0.3rem;
    font-weight: 500;
}

.schedule-location {
    color: var(--text-light);
    font-size: 0.9rem;
    margin-bottom: 1rem;
}

/* Responsive Video Styles */
@media (max-width: 768px) {
    .video-card.featured {
        grid-column: span 1;
    }

    .videos-grid {
        grid-template-columns: 1fr;
    }

    .video-meta {
        flex-direction: column;
        gap: 0.5rem;
    }

    .schedule-item {
        flex-direction: column;
        text-align: center;
    }

    .schedule-time {
        margin: 0 auto;
    }
}
